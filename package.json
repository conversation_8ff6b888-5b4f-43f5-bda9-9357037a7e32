{"name": "woovi", "license": "UNLICENSED", "scripts": {"lint": "eslint ./*.js app spec", "test": "jest"}, "main": "app.js", "dependencies": {"@google-cloud/firestore": "^7.11.3", "@google-cloud/pubsub": "^5.1.0", "@google-cloud/storage": "^7.16.0", "axios": "^1.11.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "axios-mock-adapter": "^2.1.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "globals": "^16.2.0", "jest": "^30.0.3", "prettier": "^3.6.2"}}