/** @module lib::receipt */

const crypto = require('crypto');

module.exports = {
  /**
   * Generates a unique receipt ID based on the provided endToEndId.
   * If endToEndId is falsy, returns null.
   *
   * The ID is a concatenation of endToEndId and a SHA-256 hash of
   * endToEndId with a fixed salt.
   *
   * @param {string} endToEndId - The base identifier for the receipt.
   * @returns {string|null} The generated receipt ID or null if input is invalid.
   */
  id: (endToEndId) => {
    if (!endToEndId) return null;

    return (
      endToEndId +
      crypto
        .createHash('sha256')
        .update(endToEndId + '8b18672665169bbdcb4734fca9524fd5af6b8f1cec4ded7d698f5d674e1e1fc8')
        .digest('hex')
    );
  },
};
