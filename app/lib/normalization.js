/** @module lib::normalization */

const onlyDigitsExpression = /[^a-z0-9]/gi;

const zerosOnLeftExpression = /^0*/gi;

/**
 * Very basic normalization.
 *
 * @memberof lib::normalization
 * @param { string } value The data to be normalized.
 * @returns { string } The normalized value.
 */
function basic(value) {
  return value.trim();
}

/**
 * Removes any special characters from a value.
 *
 * @memberof lib::normalization
 * @param { string } value The data to be normalized.
 * @returns { string } The normalized value.
 */
function onlyDigits(value) {
  return value.replace(onlyDigitsExpression, '');
}

/**
 * Checks for excessive zeros on left, removes them to get a "meaning" value, and finally reappends a suitable number
 * of zeros to fit the desired final size.
 *
 * @memberof lib::normalization
 * @param { string } value The data to be treated.
 * @param { number } finalSize The desired final size.
 * @returns { string } The treated value, which will be completed with zeros on left.
 */
function manageZerosOnLeft(value, finalSize) {
  return onlyDigits(value).replace(zerosOnLeftExpression, '').padStart(finalSize, '0');
}

module.exports = {
  basic,
  onlyDigits,
  manageZerosOnLeft,
};
