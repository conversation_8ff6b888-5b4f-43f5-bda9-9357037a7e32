/** @module lib::woovi */

/**
 * Converts the term used for account type from local specs to Woovi specs.
 *
 * @memberof lib::woovi
 * @param { string } type The type description in english.
 * @returns { string } The type description in portuguese.
 */
function accountType(type) {
  return { checking: 'CACC', savings: 'SVGS' }[type];
}

/**
 * Some accounts needs to be adjusted before sending to <PERSON>oo<PERSON>.
 *
 * @memberof lib::woovi
 * @param { string } bankCode The bank code.
 * @param { string } accountNumber The account number.
 * @returns { string } The account number to be used.
 */
function bankAccount(bankCode, accountNumber) {
  return bankCode === '001' ? accountNumber.replace(/X$/i, '0') : accountNumber;
}

/**
 * Converts the Bank Code (COMPE) to IBSP and Name.
 * @memberof lib::woovi
 * @param { string } bankCode The bank code.
 * @returns { object } The bank object.
 */
function pspBank(bankCode) {
  return {
    237: { id: '********', name: '<PERSON><PERSON> BRADESCO S.A.' }, // Bradesco
    '001': { id: '********', name: 'BCO DO BRASIL S.A.' }, // Banco do Brasil
    '033': { id: '********', name: 'BCO SANTANDER (BRASIL) S.A.' }, // Santander
    104: { id: '********', name: 'CAIXA ECONOMICA FEDERAL' }, // Caixa Econômica
    341: { id: '********', name: 'ITAU UNIBANCO S.A.' }, // Itaú
    399: { id: '********', name: 'BCO HSBC S.A.' }, // HSBC
    '077': { id: '********', name: 'BANCO INTER' }, // Inter
    260: { id: '********', name: 'NU PAGAMENTOS - IP' }, // NuBank
    536: { id: '********', name: 'NEON PAGAMENTOS S.A. IP' }, // Neon
    212: { id: '********', name: 'BANCO ORIGINAL' }, // Original
    380: { id: '********', name: 'PICPAY' }, // PicPay
    323: { id: '********', name: 'MERCADO PAGO IP LTDA.' }, // MercadoPago
    290: { id: '********', name: 'PAGSEGURO INTERNET IP S.A.' }, // PagBank
    336: { id: '********', name: 'BCO C6 S.A.' }, // C6 Bank
    748: { id: '********', name: 'BCO COOPERATIVO SICREDI S.A.' }, // Sicredi
    745: { id: '********', name: 'BCO CITIBANK S.A.' }, // Citibank
    318: { id: '********', name: 'BCO BMG S.A.' }, // BMG
    121: { id: '********', name: 'BCO AGIBANK S.A.' }, // AgiBank
    623: { id: '********', name: 'BANCO PAN' }, // Banco Pan
    756: { id: '********', name: 'BANCO SICOOB S.A.' }, // Sicoob
    655: { id: '********', name: 'BCO VOTORANTIM S.A.' }, // Votorantim
  }[bankCode];
}

module.exports = {
  accountType,
  bankAccount,
  pspBank,
};
