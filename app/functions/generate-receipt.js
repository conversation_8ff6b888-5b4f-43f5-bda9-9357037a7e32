/** @module functions::generateReceipt */

const { Storage } = require('@google-cloud/storage');
const axios = require('axios');
const yup = require('yup');

const log = require('../lib/log');
const receipt = require('../lib/receipt');

const storage = new Storage();

/**
 * Validates the payload for receipt generation.
 *
 * @private
 * @param {Object} data - The payload to validate.
 * @param {string} data.type - The type of receipt ('pix-in', 'pix-out', 'pix-refund').
 * @param {string} data.endToEndId - The transaction identifier.
 * @throws {yup.ValidationError} If validation fails.
 */
function validate(data) {
  const schema = yup.object({
    type: yup.string().oneOf(['pix-in', 'pix-out', 'pix-refund']).required(),
    endToEndId: yup.string().required(),
  });
  schema.validateSync(data, { abortEarly: false });
}

/**
 * Fetches the receipt pdf file from the Woovi API.
 *
 * @private
 * @param {string} type - The type of receipt.
 * @param {string} endToEndId - The transaction identifier.
 * @returns {Promise<Buffer>} The receipt file as a buffer.
 */
async function wooviReceiptFile(type, endToEndId) {
  return (
    await axios.get(`https://${process.env.WOOVI_API_ENDPOINT}/api/v1/receipt/${type}/${endToEndId}`, {
      responseType: 'arraybuffer',
      headers: { Authorization: process.env.WOOVI_API_KEY },
    })
  ).data;
}

/**
 * Stores the receipt file in Google Cloud Storage.
 *
 * @private
 * @param {string} endToEndId - The transaction identifier.
 * @param {Buffer} data - The receipt file data.
 * @returns {Promise<void>}
 */
async function putInStorage(endToEndId, data) {
  await storage
    .bucket(process.env.WOOVI_RECEIPT_BUCKET)
    .file(`${process.env.WOOVI_RECEIPT_PATH}/${receipt.id(endToEndId)}.pdf`)
    .save(data);
}

/**
 * Cloud Function handler for generating and storing a receipt PDF.
 *
 * Decodes the incoming Pub/Sub message, validates the payload, fetches
 * the receipt PDF from the Woovi API, and stores it in Google Cloud Storage.
 * Logs validation errors and rethrows unexpected errors.
 *
 * @memberof functions::generateReceipt
 * @param {Object} event - The Pub/Sub event object.
 * @returns {Promise<void>}
 */
module.exports = async (event) => {
  let payload;
  try {
    payload = JSON.parse(Buffer.from(event.data, 'base64').toString());

    validate(payload);

    await putInStorage(payload.endToEndId, await wooviReceiptFile(payload.type, payload.endToEndId));
  } catch (error) {
    if (error instanceof yup.ValidationError) {
      log.error({ data: { payload, errors: error.errors } });

      return;
    }
    log.error(error);

    throw error;
  }
};
