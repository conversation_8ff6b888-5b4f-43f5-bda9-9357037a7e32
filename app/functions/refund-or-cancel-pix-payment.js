/** @module functions::refundOrCancelPixPayment */

const { PubSub } = require('@google-cloud/pubsub');
const axios = require('axios');
const yup = require('yup');

const log = require('../lib/log');

const headers = { Authorization: process.env.WOOVI_API_KEY };
const pubSub = new PubSub();

/**
 * Checks the key to authorize the request, or not.
 *
 * @private
 * @param req { Object } {@link https://expressjs.com/en/api.html#req}
 * @returns { void }
 * @throws { Error } If key is not valid.
 */
function authorize(req) {
  if (req.get('Pfz-Key') !== process.env.PFZ_KEY) {
    throw new Error('Unauthorized');
  }
}

/**
 * Validates the received data.
 *
 * @private
 * @param { Object } data Data to be validated.
 * @returns { void }
 * @throws { yup.ValidationError } Validation errors to be logged.
 */
function validate(data) {
  const schema = yup.object({
    id: yup.string().required(),
    value: yup.number().integer().min(1),
  });
  schema.validateSync(data, { abortEarly: false });
}

/**
 * Queries the Woovi API for the transaction.
 *
 * @async
 * @private
 * @param {string} id Transaction ID.
 * @returns {Promise<Object>} The transaction information.
 */
async function queryTransaction(id) {
  const response = await axios.get(`https://${process.env.WOOVI_API_ENDPOINT}/api/v1/transaction/${id}`, {
    headers,
  });

  return {
    endToEndId: response.data.transaction.endToEndId,
    value: response.data.transaction.value,
  };
}

/**
 * Refunds a Pix transaction via the Woovi API.
 *
 * Publishes a message to the 'woovi-do-receipt' Pub/Sub topic on success.
 * Returns 'refunded' if successful.
 *
 * @async
 * @private
 * @param {Object} transaction The transaction information.
 * @param {number} value Amount to refund.
 * @returns {Promise<string>} Refund status.
 */
async function refund(transaction, value) {
  const correlationID = `${transaction.endToEndId}-${Date.now()}`;
  const valueToApply = +(value || transaction.value);
  const wooviPayload = { transactionEndToEndId: transaction.endToEndId, correlationID, value: valueToApply };

  log.info('Sending to Woovi', wooviPayload);

  const refundResponse = await axios.post(`https://${process.env.WOOVI_API_ENDPOINT}/api/v1/refund`, wooviPayload, {
    headers,
  });

  await pubSub.topic('woovi-do-receipt').publishMessage({
    data: Buffer.from(
      JSON.stringify({
        type: 'pix-refund',
        endToEndId: refundResponse.data.pixTransactionRefund.returnIdentification,
      })
    ),
  });

  return 'refunded';
}

/**
 * Handler for refunding or cancelling a Pix payment.
 *
 * Validates and authorizes the request, queries transaction details,
 * attempts to refund, and sends the appropriate HTTP response.
 *
 * @async
 * @memberof functions::refundOrCancelPixPayment
 * @param req { Object } {@link https://expressjs.com/en/api.html#req}
 * @param res { Object } {@link https://expressjs.com/en/api.html#res}
 * @returns { Promise<void> }
 */
module.exports = async (req, res) => {
  try {
    log.info('Payload', req.body);
    if (req.method !== 'POST') {
      res.status(405).send();

      return;
    }
    authorize(req);
    validate(req.body);
    const transaction = await queryTransaction(req.body.id);

    res.status(200).send({ action: await refund(transaction, req.body.value) });
  } catch (error) {
    if (error instanceof yup.ValidationError) {
      log.error({ data: { payload: req.body, errors: error.errors } });

      res.status(400).send();

      return;
    }
    error.data = req.body;
    log.error(error);

    res.status(500).send();
  }
};
