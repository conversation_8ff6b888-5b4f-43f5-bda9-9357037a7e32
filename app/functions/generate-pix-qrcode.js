/** @module functions::generatePixQrcode */

const axios = require('axios');
const yup = require('yup');

const log = require('../lib/log');
const normalization = require('../lib/normalization');

/**
 * Normalizes field values removing spaces and some non alphanumeric characters.
 *
 * @private
 * @param { Object } data The data to be normalized.
 * @returns { Object } The same data, but normalized.
 */
function normalize(data) {
  return {
    ...data,
    payer_name: normalization.basic(data.payer_name),
    payer_document: normalization.onlyDigits(data.payer_document),
  };
}

/**
 * Validates the received data.
 *
 * @private
 * @param { Object } data Data to be validated.
 * @returns { void }
 * @throws { yup.ValidationError } Validation errors to be logged.
 */
function validate(data) {
  const schema = yup.object({
    value: yup.number().integer().min(1).required(),
    payer_name: yup.string().required(),
    payer_document: yup.string().required(),
    integration_id: yup.string(),
  });
  schema.validateSync(data, { abortEarly: false });
}

/**
 * Converts the data from the external idiom to the Woovi specification.
 *
 * @private
 * @param { Object } data The data to be converted.
 * @returns { Object } The same data, but converted to the Woovi specification.
 */
function convert(data) {
  return {
    name: data.payer_name,
    correlationID: data.integration_id,
    value: data.value,
    comment: `CPF: ${data.payer_document}`,
  };
}

/**
 * Creates a QRcode and gets a base64 image using Woovi API.
 *
 * @private
 * @param { Object } data The received data to generate a QRcode and get image.
 * @returns { Promise<Object> } The QRcode data object.
 */
async function createWooviQRcode(data) {
  const headers = { Authorization: process.env.WOOVI_API_KEY };
  let qrcodeResponse;
  try {
    qrcodeResponse = await axios.post(`https://${process.env.WOOVI_API_ENDPOINT}/api/v1/qrcode-static`, data, {
      headers,
    });
  } catch (error) {
    if (error.response?.status === 400) {
      qrcodeResponse = await axios.get(
        `https://${process.env.WOOVI_API_ENDPOINT}/api/v1/qrcode-static/${data.correlationID}`,
        {
          headers,
        }
      );
    } else {
      throw error;
    }
    if (!qrcodeResponse?.data?.pixQrCode) {
      throw new Error('QR code main record not found');
    }
  }
  const result = qrcodeResponse.data.pixQrCode;
  const imageResponse = await axios.get(
    `https://${process.env.WOOVI_API_ENDPOINT}/api/image/qrcode/base64/${result.paymentLinkID}`,
    {
      headers,
    }
  );
  if (!imageResponse?.data?.imageBase64) {
    throw new Error('QR code image not found');
  }

  return {
    ...result,
    imageBase64: imageResponse.data.imageBase64.replace('data:image/png;base64,', ''),
  };
}

/**
 * Filters the Woovi response to create a meaningful response to the request.
 *
 * @private
 * @param { Object } data The received data to generate a QRcode.
 * @returns { Object } The QRcode data object.
 */
function filter(data) {
  return {
    id: data.identifier,
    code: data.brCode,
    image_base64: data.imageBase64,
  };
}

/**
 * @memberof functions::generatePixQrcode
 * @param req { Object } {@link https://expressjs.com/en/api.html#req}
 * @param res { Object } {@link https://expressjs.com/en/api.html#res}
 * @returns { Promise<void> }
 */
module.exports = async (req, res) => {
  let data;
  try {
    if (req.method !== 'POST') {
      res.status(405).send();

      return;
    }
    data = req.body;
    validate(data);
    data = normalize(data);
    data = convert(data);
    const response = await createWooviQRcode(data);

    res.status(200).send(filter(response));
  } catch (error) {
    if (error instanceof yup.ValidationError) {
      log.error({ data: { payload: data, errors: error.errors } });

      res.status(400).send();

      return;
    }
    error.data = data;
    log.error(error);

    res.status(500).send();
  }
};
