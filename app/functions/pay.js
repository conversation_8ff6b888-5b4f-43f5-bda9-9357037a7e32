/** @module functions::pay */

const axios = require('axios');
const yup = require('yup');
const { Firestore } = require('@google-cloud/firestore');

const log = require('../lib/log');
const normalization = require('../lib/normalization');
const woovi = require('../lib/woovi');
const firestore = new Firestore();

/**
 * Normalizes field values removing spaces and some non alphanumeric characters.
 *
 * @private
 * @param { Object } transfer A transfer to be normalized.
 * @returns { Object } The normalized transfer.
 */
function normalize(transfer) {
  return {
    ...transfer,
    entry_id: normalization.basic(transfer.entry_id),
    tasker_name: normalization.basic(transfer.tasker_name),
    tasker_email: normalization.basic(transfer.tasker_email),
    tasker_cpf: normalization.onlyDigits(transfer.tasker_cpf),
    bank_code: normalization.onlyDigits(transfer.bank_code),
    bank_agency: normalization.manageZerosOnLeft(transfer.bank_agency.split('-')[0], 4),
    bank_account: normalization.manageZerosOnLeft(transfer.bank_account, 20),
    bank_account_type: normalization.basic(transfer.bank_account_type),
  };
}

/**
 * Some data needs to be adjusted before sending to Woovi.
 *
 * @private
 * @param { Array } transfers List of transfers to be adjusted.
 * @returns { Array } A list of the same transfers, but adjusted according to the some Woovi rules.
 */
function applyRules(transfer) {
  return {
    ...transfer,
    bank_account: woovi.bankAccount(transfer.bank_code, transfer.bank_account),
  };
}

/**
 * Validates the transfer.
 *
 * @private
 * @param { Object } transfer Transfer to be validated.
 * @returns { void }
 * @throws { yup.ValidationError } Validation errors to be logged.
 */
function validate(transfer) {
  const schema = yup.object({
    entry_id: yup.string().required(),
    tasker_name: yup.string().required(),
    tasker_email: yup.string().email().required(),
    tasker_cpf: yup.string().required(),
    bank_code: yup.string().required(),
    bank_agency: yup.string().required(),
    bank_account: yup.string().required(),
    bank_account_type: yup
      .string()
      .matches(/\b(checking|savings)\b/)
      .required(),
    value: yup.number().integer().min(1).required(),
  });

  schema.validateSync(transfer, { abortEarly: false });
}

/**
 * Adjusts an error created by the Yup.
 *
 * @private
 * @param { yup.ValidationError } error The error object created by the Yup.
 * @returns { Error } An appropriated error object to be logged.
 */
function formatYupError(error) {
  const newErrorObject = new Error(`pay: validation exception (entry_id: ${error.value.entry_id})`);
  const errorList = [];
  error.inner.forEach((element) => {
    errorList.push(element.message);
  });
  newErrorObject.data = { errors: errorList, transfer: error.value };

  return newErrorObject;
}

/**
 * Converts the transfer from the external idiom to the Woovi specification.
 *
 * @private
 * @param { Object } transfer The transfers to be converted.
 * @returns { Object } The same transfers, but restructured as Woovi transfer.
 */
function convert(transfer) {
  return {
    value: transfer.value,
    correlationID: transfer.entry_id,
    holder: {
      name: transfer.tasker_name,
      taxID: {
        type: 'BR:CPF',
        taxID: transfer.tasker_cpf,
      },
    },
    psp: woovi.pspBank(transfer.bank_code),
    account: {
      account: transfer.bank_account,
      branch: transfer.bank_agency.slice(0, 4),
      accountType: woovi.accountType(transfer.bank_account_type),
    },
  };
}

/**
 * Creates a payment using Woovi API.
 *
 * Some important notes:
 * - Payments with value greater than R$1.500 will be created, but not approved;
 *
 * @private
 * @param { string } token Transfeera authentication token.
 * @param { Object } data The data received, containing a possible batch_name and a list of the transfers to be
 *   included in the batch.
 * @returns { Promise<boolean> } status.
 * @throws { Error } If things goes bad.
 */
async function createPayment(data) {
  const response = await axios.post(`https://${process.env.WOOVI_API_ENDPOINT}/api/v1/payment`, data, {
    headers: { Authorization: process.env.WOOVI_API_KEY },
  });
  const success = response?.data?.payment?.status === 'CREATED';

  // if (success) await incrementPaymentHistoric(data.holder.taxID.taxID, data.value);

  if (data.value <= 150000) {
    await approvePayment(data.correlationID);
  } else {
    log.warn('Payment with value greater than R$1.500 created. It needs manual approve.', {
      transfer: { entry_id: data.correlationID },
    });
  }

  return success;
}

/**
 * Increments payment's historic of the last seven days.
 *
 * It's using a document in the Firestore to control the payments made by the tasker.
 *
 * @param { string } cpf The CPF of the tasker.
 * @param { number } value The value of the payment.
 */
async function incrementPaymentHistoric(cpf, value) {
  const now = new Date();

  const document = firestore.doc(`woovi.payments/${cpf}`);
  const snapshot = await document.get();
  const data = snapshot.data() || {};

  const currentWeekday = now.getDay();
  const currentData = data[currentWeekday] || {};
  let newValue = value;
  if (currentData && currentData.updatedAt.toDate().toDateString() === now.toDateString()) {
    newValue = FieldValue.increment(value);
  }

  data[currentWeekday] = { value: newValue, updatedAt: new Date() };
  data['expiresAt'] = now;
  data['expiresAt'].setDate(now.getDate() + 7);

  await document.set(data, { merge: true });

  return true;
}

/**
 * Approves a payment using Woovi API.
 *
 * @private
 * @param { string } correlationID The correlation ID of the payment to approve.
 * @returns { Promise<boolean> } status.
 * @throws { Error } If things goes bad.
 */
async function approvePayment(correlationID) {
  const response = await axios.post(
    `https://${process.env.WOOVI_API_ENDPOINT}/api/v1/payment/approve`,
    {
      correlationID,
    },
    {
      headers: { Authorization: process.env.WOOVI_API_KEY },
    }
  );

  return response?.data?.payment?.status === 'APPROVED';
}

/**
 * @see {@link https://cloud.google.com/pubsub/docs/reference/rest/v1/PubsubMessage}
 * @memberof functions::pay
 * @param { Object } message The payload sent by pub/sub.
 * @returns { Promise<Boolean> } Just returns a true resolved value.
 * @throws { Error } If an error occurred, this function will be called again by the pub/sub.
 */
module.exports = async (message) => {
  try {
    const data = JSON.parse(Buffer.from(message.data, 'base64').toString());

    let transfer = normalize(data);
    validate(transfer);
    transfer = applyRules(transfer);

    await createPayment(convert(transfer));

    return true;
  } catch (error) {
    if (error instanceof yup.ValidationError) {
      log.error(formatYupError(error));

      return true;
    }

    log.error(error);
    throw error;
  }
};
