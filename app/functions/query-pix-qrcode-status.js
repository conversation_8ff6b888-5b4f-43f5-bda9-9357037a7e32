/** @module functions::queryPixQRcodeStatus */

const axios = require('axios');
const yup = require('yup');

const log = require('../lib/log');

/**
 * Validates the received id.
 *
 * @private
 * @param { String } id Id to be validated.
 * @returns { void }
 * @throws { yup.ValidationError } Validation errors to be logged.
 */
function validate(id) {
  const schema = yup.string().required();
  schema.validateSync(id);
}

/**
 * Query a Transaction using Woovi API.
 *
 * @private
 * @param { String } id The received QRcode id.
 * @returns { Object } The status object.
 */
async function queryWooviTransaction(id) {
  try {
    await axios.get(`https://${process.env.WOOVI_API_ENDPOINT}/api/v1/transaction/${id}`, {
      headers: { Authorization: process.env.WOOVI_API_KEY },
    });

    return { status: 'success' };
  } catch (error) {
    if (error.response?.status === 400) {
      return { status: 'pending' };
    }

    throw error;
  }
}

/**
 * @memberof functions::queryPixQRcodeStatus
 * @param req { Object } {@link https://expressjs.com/en/api.html#req}
 * @param res { Object } {@link https://expressjs.com/en/api.html#res}
 * @returns { Promise<void> }
 */
module.exports = async (req, res) => {
  let id;
  try {
    if (req.method !== 'GET') {
      res.status(405).send();

      return;
    }
    id = req.url.slice(1);
    validate(id);
    const response = await queryWooviTransaction(id);

    res.status(200).send(response);
  } catch (error) {
    if (error instanceof yup.ValidationError) {
      log.error({ data: { payload: { id }, errors: error.errors } });

      res.status(400).send();

      return;
    }
    error.data = { id };
    log.error(error);

    res.status(500).send();
  }
};
