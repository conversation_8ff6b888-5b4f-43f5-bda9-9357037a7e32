/** @module functions::check-account */

const axios = require('axios');
const yup = require('yup');

const log = require('../lib/log');
const normalization = require('../lib/normalization');
const woovi = require('../lib/woovi');

/**
 * Normalizes field values removing spaces and some non alphanumeric characters.
 *
 * @private
 * @param { Object } transfer A transfer to be normalized.
 * @returns { Object } The normalized transfer.
 */
function normalize(transfer) {
  return {
    ...transfer,
    tasker_id: normalization.basic(transfer.tasker_id),
    tasker_bank_id: normalization.basic(transfer.tasker_bank_id),
    tasker_name: normalization.basic(transfer.tasker_name),
    tasker_cpf: normalization.onlyDigits(transfer.tasker_cpf),
    bank_code: normalization.onlyDigits(transfer.bank_code),
    bank_agency: normalization.manageZerosOnLeft(transfer.bank_agency.split('-')[0], 4),
    bank_account: normalization.manageZerosOnLeft(transfer.bank_account, 20),
    bank_account_type: normalization.basic(transfer.bank_account_type),
    bank_hash: normalization.basic(transfer.bank_hash),
  };
}

/**
 * Some data needs to be adjusted before sending to Woovi.
 *
 * @private
 * @param { Array } transfers List of transfers to be adjusted.
 * @returns { Array } A list of the same transfers, but adjusted according to the some Woovi rules.
 */
function applyRules(transfer) {
  return {
    ...transfer,
    bank_account: woovi.bankAccount(transfer.bank_code, transfer.bank_account),
  };
}

/**
 * Validates the transfer.
 *
 * @private
 * @param { Object } transfer Transfer to be validated.
 * @returns { void }
 * @throws { yup.ValidationError } Validation errors to be logged.
 */
function validate(transfer) {
  const schema = yup.object({
    tasker_id: yup.string().required(),
    tasker_bank_id: yup.string().required(),
    tasker_name: yup.string().required(),
    tasker_cpf: yup.string().required(),
    bank_code: yup.string().required(),
    bank_agency: yup.string().required(),
    bank_account: yup.string().required(),
    bank_account_type: yup
      .string()
      .matches(/\b(checking|savings)\b/)
      .required(),
    bank_hash: yup.string().required(),
  });

  schema.validateSync(transfer, { abortEarly: false });
}

/**
 * Adjusts an error created by the Yup.
 *
 * @private
 * @param { yup.ValidationError } error The error object created by the Yup.
 * @returns { Error } An appropriated error object to be logged.
 */
function formatYupError(error) {
  const correlationID = `${error.value.tasker_id}:${error.value.tasker_bank_id}:${error.value.bank_hash}`;
  const newErrorObject = new Error(`check-account: validation exception (correlationID: ${correlationID})`);
  const errorList = [];
  error.inner.forEach((element) => {
    errorList.push(element.message);
  });
  newErrorObject.data = { errors: errorList, transfer: error.value };

  return newErrorObject;
}

/**
 * Converts the transfer from the external idiom to the Woovi specification.
 *
 * @private
 * @param { Object } transfer The transfers to be converted.
 * @returns { Object } The same transfers, but restructured as Woovi transfer.
 */
function convert(correlationID, transfer) {
  return {
    value: 1, // R$0,01 is the fixed value to check the account
    correlationID,
    holder: {
      name: transfer.tasker_name,
      taxID: {
        type: 'BR:CPF',
        taxID: transfer.tasker_cpf,
      },
    },
    psp: woovi.pspBank(transfer.bank_code),
    account: {
      account: transfer.bank_account,
      branch: transfer.bank_agency.slice(0, 4),
      accountType: woovi.accountType(transfer.bank_account_type),
    },
  };
}

/**
 * Creates a payment using Woovi API.
 *
 * @private
 * @param { string } token Transfeera authentication token.
 * @param { Object } data The data received, containing a possible batch_name and a list of the transfers to be
 *   included in the batch.
 * @returns { Promise<boolean> } status.
 * @throws { Error } If things goes bad.
 */
async function createPayment(data) {
  const response = await axios.post(`https://${process.env.WOOVI_API_ENDPOINT}/api/v1/payment`, data, {
    headers: { Authorization: process.env.WOOVI_API_KEY },
  });

  return response?.data?.payment?.status === 'CREATED';
}

/**
 * Approves a payment using Woovi API.
 *
 * @private
 * @param { string } correlationID The correlation ID of the payment to approve.
 * @returns { Promise<boolean> } status.
 * @throws { Error } If things goes bad.
 */
async function approvePayment(correlationID) {
  const response = await axios.post(
    `https://${process.env.WOOVI_API_ENDPOINT}/api/v1/payment/approve`,
    {
      correlationID,
    },
    {
      headers: { Authorization: process.env.WOOVI_API_KEY },
    }
  );

  return response?.data?.payment?.status === 'APPROVED';
}

/**
 * @see {@link https://cloud.google.com/pubsub/docs/reference/rest/v1/PubsubMessage}
 * @memberof functions::check-account
 * @param { Object } message The payload sent by pub/sub.
 * @returns { Promise<Boolean> } Just returns a true resolved value.
 * @throws { Error } If an error occurred, this function will be called again by the pub/sub.
 */
module.exports = async (message) => {
  try {
    const data = JSON.parse(Buffer.from(message.data, 'base64').toString());

    let transfer = normalize(data);
    validate(transfer);
    transfer = applyRules(transfer);

    const correlationID = `${transfer.tasker_id}:${transfer.tasker_bank_id}:${transfer.bank_hash}`;
    const created = await createPayment(convert(correlationID, transfer));
    const approved = await approvePayment(correlationID);

    log.info(`check-account (correlationID: ${correlationID},c:${created},a:${approved})`, {
      transfer,
      created,
      approved,
    });

    return true;
  } catch (error) {
    if (error instanceof yup.ValidationError) {
      log.error(formatYupError(error));

      return true;
    }

    log.error(error);
    throw error;
  }
};
