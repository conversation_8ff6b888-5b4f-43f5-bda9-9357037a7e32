/** @module functions::processPaymentBatch */

const { PubSub } = require('@google-cloud/pubsub');

const log = require('../lib/log');

const pubSub = new PubSub();

/**
 * Publishes the batch created event on pubsub `payments-on-batch-create` topic.
 *
 * @private
 * @param { string } batchName The used batch name.
 * @param { Array } transfers The transfers sent in the batch.
 * @returns { Promise<string> } A promise that resolves to the message ID.
 * @throws { Error } If things goes bad.
 */
async function publishBatchCreatedEvent(batchName, transfers) {
  const dataBuffer = Buffer.from(
    JSON.stringify({
      batch_name: batchName,
      entry_ids: transfers.map((transfer) => transfer.entry_id),
    })
  );

  return await pubSub.topic('payments-on-batch-create').publishMessage({
    data: dataBuffer,
  });
}

/**
 * Publishes each payment entry on pubsub `woovi-do-pay` topic.
 *
 * @private
 * @param { Object } transfer The transfer to be published.
 * @returns { Promise<string> } A promise that resolves to the message ID.
 * @throws { Error } If things goes bad.
 */
async function pay(transfer) {
  const dataBuffer = Buffer.from(JSON.stringify(transfer));

  return await pubSub.topic('woovi-do-pay').publishMessage({
    data: dataBuffer,
  });
}

/**
 * @see {@link https://cloud.google.com/pubsub/docs/reference/rest/v1/PubsubMessage}
 * @memberof functions::processPaymentBatch
 * @param { Object } message The payload sent by pub/sub.
 * @returns { Promise<Boolean> } Just returns a true resolved value.
 * @throws { Error } If an error occurred, this function will be called again by the pub/sub.
 */
module.exports = async (message) => {
  try {
    const data = JSON.parse(Buffer.from(message.data, 'base64').toString());
    if (!data.transfers.length) return true;

    const batchAmount = +(data.transfers.reduce((acc, transfer) => acc + transfer.value, 0) / 100).toFixed(2);
    log.info(`Payment batch of R$${batchAmount} received with ${data.transfers.length} row(s)`);

    const batchName = data.batch_name || `batch-${new Date().toISOString()}`;
    await Promise.all(data.transfers.map(pay));
    await publishBatchCreatedEvent(batchName, data.transfers);

    return true;
  } catch (error) {
    log.error(error);

    throw error;
  }
};
