/** @module functions::pixInfo */

const axios = require('axios');
const yup = require('yup');

const log = require('../lib/log');
const receipt = require('../lib/receipt');

/**
 * Checks the key to authorize the request, or not.
 *
 * @private
 * @param req { Object } {@link https://expressjs.com/en/api.html#req}
 * @returns { void }
 * @throws { Error } If key is not valid.
 */
function authorize(req) {
  if (req.get('Pfz-Admin-Key') !== process.env.PFZ_ADMIN_KEY) {
    throw new Error('Unauthorized');
  }
}

/**
 * Validates the received id.
 *
 * @private
 * @param { String } id id to be validated.
 * @returns { void }
 * @throws { yup.ValidationError } Validation errors to be logged.
 */
function validate(id) {
  const schema = yup.string().required();
  schema.validateSync(id);
}

/**
 * Query all Woovi transactions of a payment.
 *
 * @private
 * @param { String } id The received id.
 * @returns { Object } The response data object.
 */
async function queryWooviTransactions(id) {
  const response = await axios.get(`https://${process.env.WOOVI_API_ENDPOINT}/api/v1/transaction?pixQrCode=${id}`, {
    headers: { Authorization: process.env.WOOVI_API_KEY },
  });

  const result = { payment: null, refunds: [] };

  for (const transaction of response.data.transactions) {
    const item = {
      receipt_url: `${process.env.WOOVI_RECEIPT_URL}${receipt.id(transaction.endToEndId)}.pdf`,
      value: transaction.value,
    };
    if (transaction.type === 'PAYMENT') {
      result.payment = item;

      continue;
    }
    if (transaction.type === 'REFUND') {
      result.refunds.push(item);
    }
  }

  return result;
}

/**
 * @memberof functions::pixInfo
 * @param req { Object } {@link https://expressjs.com/en/api.html#req}
 * @param res { Object } {@link https://expressjs.com/en/api.html#res}
 * @returns { Promise<void> }
 */
module.exports = async (req, res) => {
  let id;
  try {
    if (req.method !== 'GET') {
      res.status(405).send();

      return;
    }
    authorize(req);
    id = req.url.slice(1);
    validate(id);

    res.status(200).send(await queryWooviTransactions(id));
  } catch (error) {
    if (error instanceof yup.ValidationError) {
      log.error({ data: { payload: { id }, errors: error.errors } });

      res.status(400).send();

      return;
    }
    error.data = { id };
    log.error(error);
    if (error.response?.status === 400) {
      res.status(404).send();

      return;
    }

    res.status(500).send();
  }
};
