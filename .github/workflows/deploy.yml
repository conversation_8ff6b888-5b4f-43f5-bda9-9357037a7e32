name: deploy

on:
  workflow_dispatch:
  release:
    types: [published]

env:
  PROJECT: ${{ github.event_name == 'release' && github.event.action == 'published' && 'parafuzo-infra' || 'parafuzo-qa-infra' }}

jobs:
  deploy-process-payment-batch:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    steps:
      - uses: actions/checkout@v4

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Deploy
        run: |
          gcloud functions deploy woovi-process-payment-batch \
            --project ${{ env.PROJECT }} \
            --gen2 \
            --region us-east1 \
            --runtime nodejs22 \
            --entry-point processPaymentBatch \
            --verbosity error \
            --trigger-topic payments-do-pay \
            --max-instances 1

  deploy-pay:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    steps:
      - uses: actions/checkout@v4

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Deploy
        run: |
          gcloud functions deploy woovi-pay \
            --project ${{ env.PROJECT }} \
            --gen2 \
            --region us-east1 \
            --runtime nodejs22 \
            --entry-point pay \
            --verbosity error \
            --set-env-vars "WOOVI_API_ENDPOINT=${{ vars.WOOVI_API_ENDPOINT }}" \
            --set-secrets "WOOVI_API_KEY=woovi-api-key:latest" \
            --trigger-topic woovi-do-pay \
            --max-instances ${{ vars.FUNCTION_PAY_MAX_INSTANCES }}

  deploy-check-account:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    steps:
      - uses: actions/checkout@v4

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Deploy
        run: |
          gcloud functions deploy woovi-check-account \
            --project ${{ env.PROJECT }} \
            --gen2 \
            --region us-east1 \
            --runtime nodejs22 \
            --entry-point checkAccount \
            --verbosity error \
            --set-env-vars "WOOVI_API_ENDPOINT=${{ vars.WOOVI_API_ENDPOINT }}" \
            --set-secrets "WOOVI_API_KEY=woovi-api-key:latest" \
            --trigger-topic payments-do-check-account \
            --max-instances 1

  deploy-webhook:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    steps:
      - uses: actions/checkout@v4

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Deploy
        run: |
          gcloud functions deploy woovi-webhook \
            --project ${{ env.PROJECT }} \
            --gen2 \
            --region us-east1 \
            --runtime nodejs22 \
            --entry-point webhook \
            --verbosity error \
            --set-env-vars "WOOVI_RECEIPT_URL=${{ vars.WOOVI_RECEIPT_URL }}" \
            --trigger-http \
            --allow-unauthenticated \
            --max-instances ${{ vars.FUNCTION_WEBHOOK_MAX_INSTANCES }}

  deploy-pix-cash-in-webhook:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    steps:
      - uses: actions/checkout@v4

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Deploy
        run: |
          gcloud functions deploy woovi-pix-cash-in-webhook \
            --project ${{ env.PROJECT }} \
            --gen2 \
            --region us-east1 \
            --runtime nodejs22 \
            --entry-point pixCashInWebhook \
            --verbosity error \
            --trigger-http \
            --allow-unauthenticated \
            --max-instances 5

  deploy-generate-pix-qrcode:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    steps:
      - uses: actions/checkout@v4

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Deploy
        run: |
          gcloud functions deploy woovi-generate-pix-qrcode \
            --project ${{ env.PROJECT }} \
            --gen2 \
            --region us-east1 \
            --runtime nodejs22 \
            --entry-point generatePixQRcode \
            --verbosity error \
            --set-env-vars "WOOVI_API_ENDPOINT=${{ vars.WOOVI_API_ENDPOINT }}" \
            --set-secrets "WOOVI_API_KEY=woovi-api-key:latest" \
            --trigger-http \
            --allow-unauthenticated

  deploy-query-pix-qrcode-status:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    steps:
      - uses: actions/checkout@v4

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Deploy
        run: |
          gcloud functions deploy woovi-query-pix-qrcode-status \
            --project ${{ env.PROJECT }} \
            --gen2 \
            --region us-east1 \
            --runtime nodejs22 \
            --entry-point queryPixQRcodeStatus \
            --verbosity error \
            --set-env-vars "WOOVI_API_ENDPOINT=${{ vars.WOOVI_API_ENDPOINT }}" \
            --set-secrets "WOOVI_API_KEY=woovi-api-key:latest" \
            --trigger-http \
            --allow-unauthenticated

  deploy-refund-or-cancel-pix-payment:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    steps:
      - uses: actions/checkout@v4

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Deploy
        run: |
          gcloud functions deploy woovi-refund-or-cancel-pix-payment \
            --project ${{ env.PROJECT }} \
            --gen2 \
            --region us-east1 \
            --runtime nodejs22 \
            --entry-point refundOrCancelPixPayment \
            --verbosity error \
            --set-env-vars "WOOVI_API_ENDPOINT=${{ vars.WOOVI_API_ENDPOINT }},PFZ_KEY=${{ vars.PFZ_KEY }}" \
            --set-secrets "WOOVI_API_KEY=woovi-api-key:latest" \
            --trigger-http \
            --allow-unauthenticated

  deploy-generate-receipt:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    steps:
      - uses: actions/checkout@v4

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Deploy
        run: |
          gcloud functions deploy woovi-generate-receipt \
            --project ${{ env.PROJECT }} \
            --gen2 \
            --region us-east1 \
            --runtime nodejs22 \
            --entry-point generateReceipt \
            --verbosity error \
            --set-env-vars "WOOVI_API_ENDPOINT=${{ vars.WOOVI_API_ENDPOINT }},WOOVI_RECEIPT_BUCKET=${{ vars.WOOVI_RECEIPT_BUCKET }},WOOVI_RECEIPT_PATH=${{ vars.WOOVI_RECEIPT_PATH }}" \
            --set-secrets "WOOVI_API_KEY=woovi-api-key:latest" \
            --trigger-topic woovi-do-receipt \
            --max-instances 1

  deploy-pix-info:
    runs-on: ubuntu-latest

    environment: ${{ github.event_name == 'release' && github.event.action == 'published' && 'PROD' || 'QA' }}

    steps:
      - uses: actions/checkout@v4

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Deploy
        run: |
          gcloud functions deploy woovi-pix-info \
            --project ${{ env.PROJECT }} \
            --gen2 \
            --region us-east1 \
            --runtime nodejs22 \
            --entry-point pixInfo \
            --verbosity error \
            --set-env-vars "WOOVI_API_ENDPOINT=${{ vars.WOOVI_API_ENDPOINT }},WOOVI_RECEIPT_URL=${{ vars.WOOVI_RECEIPT_URL }},PFZ_ADMIN_KEY=${{ vars.PFZ_ADMIN_KEY }}" \
            --set-secrets "WOOVI_API_KEY=woovi-api-key:latest" \
            --trigger-http \
            --allow-unauthenticated
