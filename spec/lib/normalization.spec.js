const subject = require('../../app/lib/normalization');

describe('#basic', () => {
  it('trims the value', () => {
    expect(subject.basic(' value ')).toBe('value');
  });
});

describe('#onlyDigits', () => {
  it('deletes all special digits', () => {
    expect(subject.onlyDigits(' 123-4 ')).toBe('1234');
  });
});

describe('#manageZerosOnLeft', () => {
  it('resizes the value with leading zeros', () => {
    expect(subject.manageZerosOnLeft(' 00000000000000123-4 ', 8)).toBe('00001234');
  });

  it('removes all the leading zeros', () => {
    expect(subject.manageZerosOnLeft(' 00000000000000123-4 ')).toBe('1234');
  });
});
