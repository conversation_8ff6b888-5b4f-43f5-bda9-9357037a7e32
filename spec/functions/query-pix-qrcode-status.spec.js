const axios = require('axios');
const AxiosMockAdapter = require('axios-mock-adapter');

const log = require('../../app/lib/log');

const QRCODE_ID = 'QRCODE_ID';

const PIX_ENDPOINT = `https://${process.env.WOOVI_API_ENDPOINT}/api/v1/transaction/${QRCODE_ID}`;

// Mocks the Axios.
const axiosMockAdapter = new AxiosMockAdapter(axios);

const wooviResponse = { transaction: { some: 'data' } };

axiosMockAdapter.onGet(PIX_ENDPOINT).reply(200, wooviResponse);

// Mocks the logger.
log.error = jest.fn();

// Mocks the response.
const mSend = jest.fn().mockImplementation(() => {});
const mStatus = jest.fn().mockImplementation(() => ({ send: mSend }));
const response = { status: mStatus };

const request = {
  method: 'GET',
  url: `/${QRCODE_ID}`,
};

const subject = require('../../app/functions/query-pix-qrcode-status');

beforeEach(() => {
  axiosMockAdapter.resetHistory();
  log.error.mockClear();
  mStatus.mockClear();
  mSend.mockClear();
});

describe('when Woovi responds with status 200', () => {
  beforeEach(async () => {
    await subject(request, response);
  });

  it('returns with status 200', async () => {
    expect(mStatus).toHaveBeenCalledWith(200);
  });

  it('returns a response', async () => {
    expect(mSend).toHaveBeenCalledWith({ status: 'success' });
  });
});

describe('when Woovi responds with status 400', () => {
  beforeEach(async () => {
    axiosMockAdapter.onGet(PIX_ENDPOINT).reply(400);

    await subject(request, response);
  });

  it('returns with status 200', async () => {
    expect(mStatus).toHaveBeenCalledWith(200);
  });

  it('returns a response', async () => {
    expect(mSend).toHaveBeenCalledWith({ status: 'pending' });
  });
});

describe('when the method is not a GET', () => {
  it('returns with status 405', async () => {
    await subject({ ...request, method: 'POST' }, response);

    expect(mStatus).toHaveBeenCalledWith(405);
  });
});

describe('when fails on validation because id is blank', () => {
  beforeEach(async () => {
    await subject({ ...request, url: '/' }, response);
  });

  it('returns with status 400', async () => {
    expect(mStatus).toHaveBeenCalledWith(400);
  });

  it('logs the error object', async () => {
    expect(log.error).toHaveBeenCalledWith(expect.objectContaining({ data: expect.any(Object) }));
  });

  it('logs the payload and errors object', async () => {
    expect(log.error).toHaveBeenCalledWith(
      expect.objectContaining({
        data: expect.objectContaining({
          payload: expect.objectContaining({ id: expect.any(String) }),
          errors: expect.any(Object),
        }),
      })
    );
  });
});

describe('when request gets a timeout from Woovi', () => {
  beforeEach(async () => {
    axiosMockAdapter.onGet(PIX_ENDPOINT).timeout();

    await subject(request, response);
  });

  it('logs the error', async () => {
    expect(log.error).toHaveBeenCalledWith(
      expect.objectContaining({ data: { id: QRCODE_ID }, stack: expect.any(String) })
    );
  });

  it('returns with status 500', async () => {
    expect(mStatus).toHaveBeenCalledWith(500);
  });
});
