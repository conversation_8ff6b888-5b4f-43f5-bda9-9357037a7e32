const axios = require('axios');
const AxiosMockAdapter = require('axios-mock-adapter');

const log = require('../../app/lib/log');
const receipt = require('../../app/lib/receipt');

const QRCODE_ID = 'QRCODE_ID';
const RECEIPT_URL = process.env.WOOVI_RECEIPT_URL;

const TRANSACTIONS_ENDPOINT = `https://${process.env.WOOVI_API_ENDPOINT}/api/v1/transaction?pixQrCode=${QRCODE_ID}`;

const axiosMockAdapter = new AxiosMockAdapter(axios);

log.error = jest.fn().mockResolvedValue();

const mSend = jest.fn().mockImplementation(() => {});
const mStatus = jest.fn().mockImplementation(() => ({ send: mSend }));
const response = { status: mStatus };

const request = {
  method: 'GET',
  get: (header) => ({ 'Pfz-Admin-Key': process.env.PFZ_ADMIN_KEY })[header],
  url: `/${QRCODE_ID}`,
};

const subject = require('../../app/functions/pix-info');

beforeEach(() => {
  axiosMockAdapter.reset();
  axiosMockAdapter.onGet(TRANSACTIONS_ENDPOINT).reply(200, {
    transactions: [
      {
        type: 'PAYMENT',
        endToEndId: 'endToEndId-1',
        value: 10000,
      },
      {
        type: 'REFUND',
        endToEndId: 'endToEndId-2',
        value: 3000,
      },
      {
        type: 'REFUND',
        endToEndId: 'endToEndId-3',
        value: 5000,
      },
    ],
  });
  log.error.mockClear();
  mStatus.mockClear();
  mSend.mockClear();
});

describe('when everything is ok', () => {
  beforeEach(async () => {
    await subject(request, response);
  });

  it('returns with status 200', async () => {
    expect(mStatus).toHaveBeenCalledWith(200);
  });

  it('returns a response', async () => {
    expect(mSend).toHaveBeenCalledWith({
      payment: {
        receipt_url: `${RECEIPT_URL}${receipt.id('endToEndId-1')}.pdf`,
        value: 10000,
      },
      refunds: [
        {
          receipt_url: `${RECEIPT_URL}${receipt.id('endToEndId-2')}.pdf`,
          value: 3000,
        },
        {
          receipt_url: `${RECEIPT_URL}${receipt.id('endToEndId-3')}.pdf`,
          value: 5000,
        },
      ],
    });
  });
});

describe('when the method is not a GET', () => {
  it('returns with status 405', async () => {
    await subject({ ...request, method: 'POST' }, response);

    expect(mStatus).toHaveBeenCalledWith(405);
  });
});

describe('when Pfz-Admin-Key header is not valid', () => {
  beforeEach(async () => {
    await subject(
      {
        ...request,
        get: (header) => ({ 'Pfz-Admin-Key': 'invalid' })[header],
      },
      response
    );
  });

  it('logs the error', async () => {
    expect(log.error).toHaveBeenCalled();
  });

  it('returns with status 500', async () => {
    expect(mStatus).toHaveBeenCalledWith(500);
  });
});

describe('when fails on validation because id is blank', () => {
  beforeEach(async () => {
    await subject({ ...request, url: '/' }, response);
  });

  it('returns with status 400', async () => {
    expect(mStatus).toHaveBeenCalledWith(400);
  });

  it('logs the error object', async () => {
    expect(log.error).toHaveBeenCalledWith(
      expect.objectContaining({ data: expect.objectContaining({ errors: expect.any(Array) }) })
    );
  });
});

describe('when transactions does not exists', () => {
  beforeEach(async () => {
    axiosMockAdapter.onGet(TRANSACTIONS_ENDPOINT).reply(400);

    await subject(request, response);
  });

  it('logs the error', async () => {
    expect(log.error).toHaveBeenCalled();
  });

  it('returns with status 404', async () => {
    expect(mStatus).toHaveBeenCalledWith(404);
  });
});
