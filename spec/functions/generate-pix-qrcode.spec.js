const axios = require('axios');
const AxiosMockAdapter = require('axios-mock-adapter');

const log = require('../../app/lib/log');

const axiosMockAdapter = new AxiosMockAdapter(axios);

// Mocks the logger.
log.error = jest.fn();
log.info = jest.fn();

// Mocks the response.
const mSend = jest.fn().mockImplementation(() => {});
const mStatus = jest.fn().mockImplementation(() => ({ send: mSend }));
const response = { status: mStatus };

const payload = {
  value: 8000,
  payer_name: '<PERSON> ',
  payer_document: '549.985.500-55',
  integration_id: '6303ee6d28902e2e0298b7e3',
};

const PIX_ENDPOINT = `https://${process.env.WOOVI_API_ENDPOINT}/api/v1/qrcode-static`;
const IMAGE_ENDPOINT = `https://${process.env.WOOVI_API_ENDPOINT}/api/image/qrcode/base64/paymentLinkID`;
const QUERY_ENDPOINT = `https://${process.env.WOOVI_API_ENDPOINT}/api/v1/qrcode-static/${payload.integration_id}`;

/**
 * Generates a valid request object.
 *
 * @param { object } body The body information.
 * @returns { object } The request object.
 */
function request(body, method = 'POST') {
  return {
    body,
    method,
  };
}

const subject = require('../../app/functions/generate-pix-qrcode');

beforeEach(() => {
  axiosMockAdapter.reset();
  axiosMockAdapter.onPost(PIX_ENDPOINT).reply(200, {
    pixQrCode: {
      identifier: 'id',
      paymentLinkID: 'paymentLinkID',
      brCode: 'code',
    },
  });
  axiosMockAdapter.onGet(IMAGE_ENDPOINT).reply(200, {
    imageBase64: 'data:image/png;base64,imageBase64',
  });
  axiosMockAdapter.onGet(QUERY_ENDPOINT).reply(200, {
    pixQrCode: {
      identifier: 'id',
      paymentLinkID: 'paymentLinkID',
      brCode: 'code',
    },
  });
  log.error.mockClear();
  mStatus.mockClear();
  mSend.mockClear();
});

describe('when receives a good request', () => {
  beforeEach(async () => {
    await subject(request(payload), response);
  });

  it('normalizes (trim) payer_name as name', async () => {
    expect(JSON.parse(axiosMockAdapter.history.post[0].data).name).toBe('John Doe');
  });

  it('normalizes the received payer_document as comment', async () => {
    expect(JSON.parse(axiosMockAdapter.history.post[0].data).comment).toBe('CPF: 54998550055');
  });

  it('sends the integration_id as correlationID', async () => {
    expect(JSON.parse(axiosMockAdapter.history.post[0].data).correlationID).toBe(payload.integration_id);
  });

  it('sends the value as is', async () => {
    expect(JSON.parse(axiosMockAdapter.history.post[0].data).value).toBe(payload.value);
  });

  it('returns with status 200', async () => {
    expect(mStatus).toHaveBeenCalledWith(200);
  });

  it('returns a valid response', async () => {
    expect(mSend).toHaveBeenCalledWith({ id: 'id', code: 'code', image_base64: 'imageBase64' });
  });
});

describe('when the method is not a POST', () => {
  it('returns with status 405', async () => {
    await subject(request(payload, 'GET'), response);

    expect(mStatus).toHaveBeenCalledWith(405);
  });
});

describe('when fails on validation', () => {
  beforeEach(async () => {
    await subject(request({}), response);
  });

  it('returns with status 400', async () => {
    expect(mStatus).toHaveBeenCalledWith(400);
  });

  it('logs the payload and errors object', async () => {
    expect(log.error).toHaveBeenCalledWith(
      expect.objectContaining({
        data: expect.objectContaining({
          payload: expect.any(Object),
          errors: expect.any(Object),
        }),
      })
    );
  });
});

describe('when qrcode already exists', () => {
  beforeEach(async () => {
    axiosMockAdapter.onPost(PIX_ENDPOINT).reply(400);

    await subject(request(payload), response);
  });

  it('returns with status 200', async () => {
    expect(mStatus).toHaveBeenCalledWith(200);
  });

  it('returns a valid response', async () => {
    expect(mSend).toHaveBeenCalledWith({ id: 'id', code: 'code', image_base64: 'imageBase64' });
  });
});

describe('when qrcode creation gets an error', () => {
  beforeEach(async () => {
    axiosMockAdapter.onPost(PIX_ENDPOINT).reply(500);

    await subject(request(payload), response);
  });

  it('logs the error', async () => {
    expect(log.error).toHaveBeenCalledWith(
      expect.objectContaining({ data: expect.any(Object), stack: expect.any(String) })
    );
  });

  it('returns with status 500', async () => {
    expect(mStatus).toHaveBeenCalledWith(500);
  });
});

describe('when does not get a valid qrcode', () => {
  beforeEach(async () => {
    axiosMockAdapter.onPost(PIX_ENDPOINT).reply(400);
    axiosMockAdapter.onGet(QUERY_ENDPOINT).reply(200, {});

    await subject(request(payload), response);
  });

  it('logs the error', async () => {
    expect(log.error).toHaveBeenCalledWith(
      expect.objectContaining({ data: expect.any(Object), stack: expect.any(String) })
    );
  });

  it('returns with status 500', async () => {
    expect(mStatus).toHaveBeenCalledWith(500);
  });
});

describe('when does not get a valid image', () => {
  beforeEach(async () => {
    axiosMockAdapter.onGet(IMAGE_ENDPOINT).reply(500);

    await subject(request(payload), response);
  });

  it('logs the error', async () => {
    expect(log.error).toHaveBeenCalledWith(
      expect.objectContaining({ data: expect.any(Object), stack: expect.any(String) })
    );
  });

  it('returns with status 500', async () => {
    expect(mStatus).toHaveBeenCalledWith(500);
  });
});
