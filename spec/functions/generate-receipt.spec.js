const AxiosMockAdapter = require('axios-mock-adapter');
const axios = require('axios');
const { Storage } = require('@google-cloud/storage');

const log = require('../../app/lib/log');
const receipt = require('../../app/lib/receipt');
log.error = jest.fn();

const RECEIPT_ENDPOINT = `https://${process.env.WOOVI_API_ENDPOINT}/api/v1/receipt`;
const axiosMockAdapter = new AxiosMockAdapter(axios);

jest.mock('@google-cloud/storage');
const mSave = jest.fn().mockResolvedValue();
const mFile = jest.fn().mockReturnValue({ save: mSave });
const mBucket = jest.fn().mockReturnValue({ file: mFile });
Storage.mockImplementation(() => ({ bucket: mBucket }));

const payload = {
  type: 'pix-refund',
  endToEndId: 'endToEndId',
};

const event = (data) => {
  return { data: Buffer.from(JSON.stringify(data)).toString('base64') };
};

const subject = require('../../app/functions/generate-receipt');

beforeEach(() => {
  axiosMockAdapter.resetHistory();
  log.error.mockClear();
});

describe('good cases', () => {
  beforeEach(async () => {
    axiosMockAdapter.onGet(`${RECEIPT_ENDPOINT}/pix-refund/endToEndId`).reply(200, 'binary');

    await subject(event(payload));
  });

  it('gets receipt file from Woovi', async () => {
    expect(axiosMockAdapter.history[0].url).toBe(`${RECEIPT_ENDPOINT}/pix-refund/endToEndId`);
  });

  it('uses the proper authorization header', async () => {
    expect(axiosMockAdapter.history[0].headers.Authorization).toBe(process.env.WOOVI_API_KEY);
  });

  it('uses the proper responseType', async () => {
    expect(axiosMockAdapter.history[0].responseType).toBe('arraybuffer');
  });

  it('uses the configured bucket', async () => {
    expect(mBucket).toHaveBeenCalledWith(process.env.WOOVI_RECEIPT_BUCKET);
  });

  it('uses the proper file to put in bucket', async () => {
    expect(mFile).toHaveBeenCalledWith(`${process.env.WOOVI_RECEIPT_PATH}/${receipt.id('endToEndId')}.pdf`);
  });

  it('saves the received receipt data', async () => {
    expect(mSave).toHaveBeenCalledWith('binary');
  });
});

describe('validation cases', () => {
  it('validates the type', async () => {
    const invalidPayload = { ...payload, type: 'invalid-type' };
    await subject(event(invalidPayload));

    expect(log.error).toHaveBeenCalledWith(
      expect.objectContaining({
        data: expect.objectContaining({
          payload: expect.objectContaining(invalidPayload),
          errors: expect.any(Object),
        }),
      })
    );
  });

  it('validates the endToEndId', async () => {
    const invalidPayload = { ...payload, endToEndId: '' };
    await subject(event(invalidPayload));

    expect(log.error).toHaveBeenCalledWith(
      expect.objectContaining({
        data: expect.objectContaining({
          payload: expect.objectContaining(invalidPayload),
          errors: expect.any(Object),
        }),
      })
    );
  });
});

describe('failure cases', () => {
  beforeEach(async () => {
    axiosMockAdapter.onGet(`${RECEIPT_ENDPOINT}/pix-refund/endToEndId`).reply(500);
  });

  it('logs the error', async () => {
    try {
      await subject(event(payload));
    } catch {
      expect(log.error).toHaveBeenCalled();
    }
  });

  it('throws the error', async () => {
    await expect(subject(event(payload))).rejects.toThrow('Request failed with status code 500');
  });
});
