const AxiosMockAdapter = require('axios-mock-adapter');
const axios = require('axios');
const { PubSub } = require('@google-cloud/pubsub');

const log = require('../../app/lib/log');

const QUERY_TRANSACTION_ENDPOINT = `https://${process.env.WOOVI_API_ENDPOINT}/api/v1/transaction/ID`;
const REFUND_ENDPOINT = `https://${process.env.WOOVI_API_ENDPOINT}/api/v1/refund`;

// Mocks the Axios.
const axiosMockAdapter = new AxiosMockAdapter(axios);

axiosMockAdapter.onGet(QUERY_TRANSACTION_ENDPOINT).reply(200, {
  transaction: {
    endToEndId: 'END2ENDID',
    value: 11100,
  },
});

axiosMockAdapter.onPost(REFUND_ENDPOINT).reply(200, {
  pixTransactionRefund: {
    returnIdentification: 'RETURNID',
  },
});

// Mocks the PubSub.
jest.mock('@google-cloud/pubsub');
const mPublish = jest.fn().mockResolvedValue();
const mTopic = jest.fn().mockReturnValue({ publishMessage: mPublish });
PubSub.mockImplementation(() => ({ topic: mTopic }));

// Mocks the logger.
log.info = jest.fn().mockResolvedValue();
log.error = jest.fn().mockResolvedValue();

// Mocks the response.
const mSend = jest.fn().mockImplementation(() => {});
const mStatus = jest.fn().mockImplementation(() => ({ send: mSend }));
const response = { status: mStatus };

const payload = { id: 'ID', value: 2345 };

function request(body, method = 'POST') {
  return {
    body,
    get: (header) => ({ 'Pfz-Key': process.env.PFZ_KEY })[header],
    method,
  };
}

const subject = require('../../app/functions/refund-or-cancel-pix-payment');

beforeEach(() => {
  axiosMockAdapter.resetHistory();
  log.info.mockClear();
  log.error.mockClear();
  mStatus.mockClear();
  mSend.mockClear();
});

describe('when the function is called', () => {
  it('logs the payload', async () => {
    await subject(request(payload), response);

    expect(log.info).toHaveBeenCalledWith('Payload', payload);
  });
});

describe('when a value is informed (common case)', () => {
  beforeEach(async () => {
    await subject(request(payload), response);
  });

  it('logs the Woovi payload', async () => {
    expect(log.info).toHaveBeenCalledWith('Sending to Woovi', {
      transactionEndToEndId: 'END2ENDID',
      correlationID: expect.any(String),
      value: 2345,
    });
  });

  it('uses the proper url', async () => {
    expect(axiosMockAdapter.history.post[0].url).toBe(REFUND_ENDPOINT);
  });

  it('uses the proper authorization header', async () => {
    expect(axiosMockAdapter.history.post[0].headers.Authorization).toBe(process.env.WOOVI_API_KEY);
  });

  it('calls the Woovi API to refund the transaction', async () => {
    expect(JSON.parse(axiosMockAdapter.history.post[0].data)).toMatchObject({
      transactionEndToEndId: 'END2ENDID',
      correlationID: expect.any(String),
      value: 2345,
    });
  });

  it('publishes in the `woovi-do-receipt` topic of pubsub', async () => {
    expect(mTopic).toHaveBeenCalledWith('woovi-do-receipt');
  });

  it('sends the endToEndId to pubsub', async () => {
    expect(mPublish).toHaveBeenCalledWith({
      data: Buffer.from(
        JSON.stringify({
          type: 'pix-refund',
          endToEndId: 'RETURNID',
        })
      ),
    });
  });

  it('returns a refunded action information', async () => {
    expect(mSend).toHaveBeenCalledWith({ action: 'refunded' });
  });

  it('returns with status 200', async () => {
    expect(mStatus).toHaveBeenCalledWith(200);
  });
});

describe('when a value is not informed', () => {
  beforeEach(async () => {
    await subject(request({ id: 'ID' }), response);
  });

  it('uses the transaction value to do a complete refund', async () => {
    expect(JSON.parse(axiosMockAdapter.history.post[0].data)).toMatchObject({
      transactionEndToEndId: 'END2ENDID',
      correlationID: expect.any(String),
      value: 11100,
    });
  });
});

describe('when a value is informed as string', () => {
  it('calls the Woovi API using the value as integer', async () => {
    await subject(request({ ...payload, value: '2345' }), response);

    expect(JSON.parse(axiosMockAdapter.history.post[0].data)).toMatchObject({
      transactionEndToEndId: 'END2ENDID',
      correlationID: expect.any(String),
      value: 2345,
    });
  });
});

describe('when getting the transaction leads to an error (any error)', () => {
  beforeEach(async () => {
    axiosMockAdapter.onGet(QUERY_TRANSACTION_ENDPOINT).reply(400);

    await subject(request(payload), response);
  });

  it('logs an info', async () => {
    expect(log.error).toHaveBeenCalled();
  });

  it('returns with status 200', async () => {
    expect(mStatus).toHaveBeenCalledWith(500);
  });
});

describe('when the method is not a POST', () => {
  it('returns with status 405', async () => {
    await subject(request(payload, 'GET'), response);

    expect(mStatus).toHaveBeenCalledWith(405);
  });
});

describe('when Pfz-Key header is not valid', () => {
  beforeEach(async () => {
    await subject(
      {
        ...request(payload),
        get: (header) => ({ 'Pfz-Key': 'invalid' })[header],
      },
      response
    );
  });

  it('logs the error', async () => {
    expect(log.error).toHaveBeenCalled();
  });

  it('returns with status 500', async () => {
    expect(mStatus).toHaveBeenCalledWith(500);
  });
});

describe('when fails on validation', () => {
  beforeEach(async () => {
    await subject(request({}), response);
  });

  it('returns with status 400', async () => {
    expect(mStatus).toHaveBeenCalledWith(400);
  });

  it('logs the payload and errors object', async () => {
    expect(log.error).toHaveBeenCalledWith(
      expect.objectContaining({
        data: expect.objectContaining({
          payload: expect.any(Object),
          errors: expect.any(Object),
        }),
      })
    );
  });
});
