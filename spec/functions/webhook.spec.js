const log = require('../../app/lib/log');
const receipt = require('../../app/lib/receipt');

log.error = jest.fn().mockResolvedValue();
log.info = jest.fn().mockResolvedValue();

const mockPublish = jest.fn().mockResolvedValue();
const mockTopic = jest.fn().mockReturnValue({
  publishMessage: mockPublish,
});

jest.mock('@google-cloud/pubsub', () => ({
  PubSub: jest.fn().mockImplementation(() => ({
    topic: mockTopic,
  })),
}));

const subject = require('../../app/functions/webhook');

const mockRequest = (body) => ({
  body,
});

const mockResponse = () => {
  const res = {};

  res.status = jest.fn().mockReturnValue(res);
  res.send = jest.fn().mockReturnValue(res);

  return res;
};

beforeEach(() => {
  jest.clearAllMocks();

  log.error.mockClear();
  log.info.mockClear();

  mockPublish.mockClear();
  mockTopic.mockClear();
});

describe('#webhook', () => {
  describe('when receives a valid payment notification', () => {
    const validNotification = {
      payment: {
        correlationID: '582f87f7985e540855000001',
        status: 'CONFIRMED',
      },
      transaction: {
        endToEndId: 'endToEndId',
      },
    };

    it('responds with status 200', async () => {
      const req = mockRequest(validNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.send).toHaveBeenCalledWith();
    });

    it('logs the webhook request', async () => {
      const req = mockRequest(validNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(log.info).toHaveBeenCalledWith('webhook', validNotification);
    });

    it('publishes converted notification to PubSub topic', async () => {
      const req = mockRequest(validNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(mockTopic).toHaveBeenCalledWith('payments-on-status-update');
      expect(mockPublish).toHaveBeenCalledWith({
        data: Buffer.from(
          JSON.stringify({
            entry_id: '582f87f7985e540855000001',
            bank_receipt_url: `${process.env.WOOVI_RECEIPT_URL}${receipt.id('endToEndId')}.pdf`,
            status: 'success',
            gateway: 'woovi',
          })
        ),
      });
    });

    it('publishes to woovi-do-receipt PubSub topic', async () => {
      const req = mockRequest(validNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(mockTopic).toHaveBeenCalledWith('woovi-do-receipt');
      expect(mockPublish).toHaveBeenCalledWith({
        data: Buffer.from(JSON.stringify({ type: 'pix-out', endToEndId: 'endToEndId' })),
      });
    });
  });

  describe('when receives payment notification with FAILED status', () => {
    const failedNotification = {
      payment: {
        correlationID: '582f87f7985e540855000002',
        status: 'FAILED',
      },
    };

    it("converts status to 'fail'", async () => {
      const req = mockRequest(failedNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(mockPublish).toHaveBeenCalledWith({
        data: Buffer.from(
          JSON.stringify({
            entry_id: '582f87f7985e540855000002',
            bank_receipt_url: null,
            status: 'fail',
            gateway: 'woovi',
          })
        ),
      });
    });

    it('does not publish to woovi-do-receipt PubSub topic', async () => {
      const req = mockRequest(failedNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(mockTopic).not.toHaveBeenCalledWith('woovi-do-receipt');
    });
  });

  describe('when receives a valid account check notification', () => {
    const validNotification = {
      payment: {
        correlationID: '582f87f7985e540855000001:582f87f7985e540855000002:582f87f7985e540855000003',
        status: 'CONFIRMED',
      },
    };

    it('responds with status 200', async () => {
      const req = mockRequest(validNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.send).toHaveBeenCalledWith();
    });

    it('logs the webhook request', async () => {
      const req = mockRequest(validNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(log.info).toHaveBeenCalledWith('webhook', validNotification);
    });

    it('publishes converted notification to PubSub topic', async () => {
      const req = mockRequest(validNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(mockTopic).toHaveBeenCalledWith('payments-on-account-check');
      expect(mockPublish).toHaveBeenCalledWith({
        data: Buffer.from(
          JSON.stringify({
            tasker_id: '582f87f7985e540855000001',
            tasker_bank_id: '582f87f7985e540855000002',
            bank_hash: '582f87f7985e540855000003',
            status: 'success',
            errors: [],
            gateway: 'woovi',
          })
        ),
      });
    });

    it('does not publish to woovi-do-receipt PubSub topic', async () => {
      const req = mockRequest(validNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(mockTopic).not.toHaveBeenCalledWith('woovi-do-receipt');
    });
  });

  describe('when receives account check notification with FAILED status', () => {
    const failedNotification = {
      payment: {
        correlationID: '582f87f7985e540855000001:582f87f7985e540855000002:582f87f7985e540855000003',
        status: 'FAILED',
      },
      transaction: {
        providerRejectedReason: 'AC03 - Número de conta inválido do credor do Pix',
      },
    };

    it("converts status to 'fail'", async () => {
      const req = mockRequest(failedNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(mockPublish).toHaveBeenCalledWith({
        data: Buffer.from(
          JSON.stringify({
            tasker_id: '582f87f7985e540855000001',
            tasker_bank_id: '582f87f7985e540855000002',
            bank_hash: '582f87f7985e540855000003',
            status: 'fail',
            errors: ['AC03 - Número de conta inválido do credor do Pix'],
            gateway: 'woovi',
          })
        ),
      });
    });

    it('does not publish to woovi-do-receipt PubSub topic', async () => {
      const req = mockRequest(failedNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(mockTopic).not.toHaveBeenCalledWith('woovi-do-receipt');
    });
  });

  describe('when receives payment notification with FAILED status', () => {
    const failedNotification = {
      payment: {
        correlationID: '582f87f7985e540855000002',
        status: 'FAILED',
      },
    };

    it("converts status to 'fail'", async () => {
      const req = mockRequest(failedNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(mockPublish).toHaveBeenCalledWith({
        data: Buffer.from(
          JSON.stringify({
            entry_id: '582f87f7985e540855000002',
            bank_receipt_url: null,
            status: 'fail',
            gateway: 'woovi',
          })
        ),
      });
    });

    it('does not publish to woovi-do-receipt PubSub topic', async () => {
      const req = mockRequest(failedNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(mockTopic).not.toHaveBeenCalledWith('woovi-do-receipt');
    });
  });

  describe('when receives notification with unknown status', () => {
    const unknownStatusNotification = {
      payment: {
        correlationID: '582f87f7985e540855000003',
        status: 'PENDING',
      },
    };

    it('converts status to undefined', async () => {
      const req = mockRequest(unknownStatusNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(mockPublish).toHaveBeenCalledWith({
        data: Buffer.from(
          JSON.stringify({
            entry_id: '582f87f7985e540855000003',
            bank_receipt_url: null,
            status: undefined,
            gateway: 'woovi',
          })
        ),
      });
    });

    it('does not publish to woovi-do-receipt PubSub topic', async () => {
      const req = mockRequest(unknownStatusNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(mockTopic).not.toHaveBeenCalledWith('woovi-do-receipt');
    });
  });

  describe('when receives notification with missing payment data', () => {
    const incompleteNotification = {
      payment: {},
    };

    it('handles missing correlationID and status gracefully', async () => {
      const req = mockRequest(incompleteNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.send).toHaveBeenCalledWith();
      expect(log.error).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  describe('when receives notification with null payment', () => {
    const nullPaymentNotification = {
      payment: null,
    };

    it('handles null payment gracefully', async () => {
      const req = mockRequest(nullPaymentNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(mockPublish).toHaveBeenCalledWith({
        data: Buffer.from(
          JSON.stringify({
            entry_id: undefined,
            bank_receipt_url: null,
            status: undefined,
            gateway: 'woovi',
          })
        ),
      });
    });

    it('does not publish to woovi-do-receipt PubSub topic', async () => {
      const req = mockRequest(nullPaymentNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(mockTopic).not.toHaveBeenCalledWith('woovi-do-receipt');
    });
  });

  describe('when receives empty notification', () => {
    const emptyNotification = {};

    it('handles empty notification gracefully', async () => {
      const req = mockRequest(emptyNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(mockPublish).toHaveBeenCalledWith({
        data: Buffer.from(
          JSON.stringify({
            entry_id: undefined,
            bank_receipt_url: null,
            status: undefined,
            gateway: 'woovi',
          })
        ),
      });
    });

    it('does not publish to woovi-do-receipt PubSub topic', async () => {
      const req = mockRequest(emptyNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(mockTopic).not.toHaveBeenCalledWith('woovi-do-receipt');
    });
  });

  describe('when PubSub publish fails', () => {
    const validNotification = {
      payment: {
        correlationID: '582f87f7985e540855000001',
        status: 'CONFIRMED',
      },
    };

    beforeEach(() => {
      const error = new Error('PubSub publish failed');
      mockPublish.mockRejectedValue(error);
    });

    it('responds with status 500', async () => {
      const req = mockRequest(validNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.send).toHaveBeenCalledWith();
    });

    it('logs the error', async () => {
      const req = mockRequest(validNotification);
      const res = mockResponse();

      await subject(req, res);

      expect(log.error).toHaveBeenCalledWith(expect.any(Error));
      expect(log.error).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'PubSub publish failed',
        })
      );
    });
  });

  describe('status conversion', () => {
    it("converts 'confirmed' to 'success' (case insensitive)", async () => {
      const transaction = { endToEndId: 'endToEndId' };
      const notifications = [
        { payment: { correlationID: 'test1', status: 'CONFIRMED' }, transaction },
        { payment: { correlationID: 'test2', status: 'confirmed' }, transaction },
        { payment: { correlationID: 'test3', status: 'Confirmed' }, transaction },
      ];

      for (const notification of notifications) {
        const req = mockRequest(notification);
        const res = mockResponse();

        await subject(req, res);

        expect(mockPublish).toHaveBeenCalledWith({
          data: Buffer.from(
            JSON.stringify({
              entry_id: notification.payment.correlationID,
              bank_receipt_url: `${process.env.WOOVI_RECEIPT_URL}${receipt.id('endToEndId')}.pdf`,
              status: 'success',
              gateway: 'woovi',
            })
          ),
        });
      }
    });

    it("converts 'failed' to 'fail' (case insensitive)", async () => {
      const notifications = [
        { payment: { correlationID: 'test1', status: 'FAILED' } },
        { payment: { correlationID: 'test2', status: 'failed' } },
        { payment: { correlationID: 'test3', status: 'Failed' } },
      ];

      for (const notification of notifications) {
        const req = mockRequest(notification);
        const res = mockResponse();

        await subject(req, res);

        expect(mockPublish).toHaveBeenCalledWith({
          data: Buffer.from(
            JSON.stringify({
              entry_id: notification.payment.correlationID,
              bank_receipt_url: null,
              status: 'fail',
              gateway: 'woovi',
            })
          ),
        });
      }
    });
  });
});
