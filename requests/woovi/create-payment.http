# Create Payment
POST {{woovi_api_url}}/api/v1/payment
Authorization: {{woovi_api_key}}
Content-Type: application/json

{
  "value": 1,
  "correlationID": "687122ea421aa900cc3504fz",
  "holder": {
    "name": "LEONARDO SILVA DE OLIVEIRA SARAIVA",
    "taxID": {
      "type": "BR:CPF",
      "taxID": "***********"
    }
  },
  "psp": {
    "id": "********",
    "name": "BCO DO BRASIL S.A."
  },
  "account": {
    "account": "********000000050601",
    "branch": "1458",
    "accountType": "CACC"
  }
}

###

# Approve Payment
POST {{woovi_api_url}}/api/v1/payment/approve
Authorization: {{woovi_api_key}}
Content-Type: application/json

{
  "correlationID": "687122ea421aa900cc3504fz"
}
